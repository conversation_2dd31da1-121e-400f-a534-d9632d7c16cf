"""
Protocol Communication Simulator - Enhanced protocol testing capabilities
Comprehensive protocol communication simulation for device testing including:
- DNP3 outstation simulation with configurable data points
- Modbus slave simulation with register mapping
- IEC 61850 server simulation with logical nodes
- File transfer protocol simulation (FTP, SFTP, HTTP)
- Communication error simulation and recovery testing
- Protocol timing and latency simulation
- Security testing with authentication failures
- Load testing with concurrent protocol sessions
- Message logging and protocol analysis
- Integration with Triangle MicroWorks SCADA gateway
"""

import asyncio
import logging
import struct
import random
import time
import hashlib
import base64
import json
import ssl
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Callable, Tuple, Set
from enum import Enum
from pathlib import Path
import socket
from dataclasses import dataclass, field
import aiohttp

# Protocol message logging
class ProtocolMessage:
    """Protocol message for logging and analysis"""
    
    def __init__(self, protocol: str, direction: str, data: bytes, 
                 timestamp: Optional[datetime] = None, metadata: Optional[Dict] = None):
        self.protocol = protocol
        self.direction = direction  # 'TX' or 'RX'
        self.data = data
        self.timestamp = timestamp or datetime.now(timezone.utc)
        self.metadata = metadata or {}
        self.message_id = str(uuid.uuid4())
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "message_id": self.message_id,
            "protocol": self.protocol,
            "direction": self.direction,
            "timestamp": self.timestamp.isoformat(),
            "data_length": len(self.data),
            "data_hex": self.data.hex(),
            "metadata": self.metadata
        }

class CommunicationError(Enum):
    """Types of communication errors to simulate"""
    TIMEOUT = "timeout"
    CHECKSUM_ERROR = "checksum_error"
    FRAME_ERROR = "frame_error"
    AUTHENTICATION_FAILURE = "auth_failure"
    CONNECTION_LOST = "connection_lost"
    BUFFER_OVERFLOW = "buffer_overflow"
    PROTOCOL_VIOLATION = "protocol_violation"
    NETWORK_CONGESTION = "network_congestion"

@dataclass
class ProtocolConfig:
    """Configuration for protocol simulation"""
    enabled: bool = True
    port: int = 20000
    max_connections: int = 10
    response_delay_ms: int = 0
    error_rate: float = 0.0  # 0.0 to 1.0
    authentication_required: bool = False
    ssl_enabled: bool = False
    message_logging: bool = True
    load_testing: bool = False
    concurrent_sessions: int = 1

@dataclass 
class DNP3Config(ProtocolConfig):
    """DNP3-specific configuration"""
    outstation_address: int = 1
    master_address: int = 100
    unsolicited_enabled: bool = True
    integrity_poll_interval: int = 30
    analog_inputs: Dict[int, float] = field(default_factory=dict)
    binary_inputs: Dict[int, bool] = field(default_factory=dict)
    counters: Dict[int, int] = field(default_factory=dict)
    control_outputs: Dict[int, bool] = field(default_factory=dict)

@dataclass
class ModbusConfig(ProtocolConfig):
    """Modbus-specific configuration"""
    slave_address: int = 1
    coils: Dict[int, bool] = field(default_factory=dict)
    discrete_inputs: Dict[int, bool] = field(default_factory=dict)
    holding_registers: Dict[int, int] = field(default_factory=dict)
    input_registers: Dict[int, int] = field(default_factory=dict)

@dataclass
class IEC61850Config(ProtocolConfig):
    """IEC 61850-specific configuration"""
    ied_name: str = "SIMULATED_IED"
    logical_devices: Dict[str, Dict] = field(default_factory=dict)
    datasets: Dict[str, List[str]] = field(default_factory=dict)
    reports: Dict[str, Dict] = field(default_factory=dict)
    goose_enabled: bool = True
    sampled_values_enabled: bool = False

class ProtocolMessageLogger:
    """Enhanced protocol message logging and analysis"""
    
    def __init__(self, log_directory: str = "protocol_logs"):
        self.logger = logging.getLogger("ProtocolMessageLogger")
        self.log_dir = Path(log_directory)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Message storage
        self.messages: List[ProtocolMessage] = []
        self.max_messages = 10000
        
        # Statistics
        self.stats = {
            "total_messages": 0,
            "messages_by_protocol": {},
            "errors_by_type": {},
            "average_response_time": 0.0,
            "peak_message_rate": 0.0
        }
        
        # Performance tracking
        self.message_timestamps = []
        self.response_times = []
        
    def log_message(self, message: ProtocolMessage):
        """Log a protocol message"""
        self.messages.append(message)
        
        # Maintain message limit
        if len(self.messages) > self.max_messages:
            self.messages.pop(0)
            
        # Update statistics
        self.stats["total_messages"] += 1
        protocol_count = self.stats["messages_by_protocol"].get(message.protocol, 0)
        self.stats["messages_by_protocol"][message.protocol] = protocol_count + 1
        
        # Track message rate
        current_time = time.time()
        self.message_timestamps.append(current_time)
        
        # Keep only last minute of timestamps
        cutoff_time = current_time - 60.0
        self.message_timestamps = [t for t in self.message_timestamps if t > cutoff_time]
        
        # Calculate message rate
        if len(self.message_timestamps) > 1:
            rate = len(self.message_timestamps) / 60.0
            if rate > self.stats["peak_message_rate"]:
                self.stats["peak_message_rate"] = rate
                
        # Log to file periodically
        if self.stats["total_messages"] % 100 == 0:
            asyncio.create_task(self._write_log_file())
            
    def log_error(self, protocol: str, error_type: CommunicationError, details: str):
        """Log a communication error"""
        error_count = self.stats["errors_by_type"].get(error_type.value, 0)
        self.stats["errors_by_type"][error_type.value] = error_count + 1
        
        self.logger.warning(f"{protocol} {error_type.value}: {details}")
        
    def log_response_time(self, response_time_ms: float):
        """Log response time for performance analysis"""
        self.response_times.append(response_time_ms)
        
        # Keep only last 1000 response times
        if len(self.response_times) > 1000:
            self.response_times.pop(0)
            
        # Update average
        if self.response_times:
            self.stats["average_response_time"] = sum(self.response_times) / len(self.response_times)
            
    async def _write_log_file(self):
        """Write messages to log file"""
        try:
            log_file = self.log_dir / f"protocol_log_{datetime.now(timezone.utc).strftime('%Y%m%d')}.json"
            
            # Get recent messages
            recent_messages = self.messages[-100:] if len(self.messages) > 100 else self.messages
            
            log_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "statistics": self.stats,
                "recent_messages": [msg.to_dict() for msg in recent_messages]
            }
            
            with open(log_file, 'w') as f:
                json.dump(log_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to write log file: {e}")
            
    def get_statistics(self) -> Dict[str, Any]:
        """Get protocol communication statistics"""
        return {
            **self.stats,
            "current_message_rate": len(self.message_timestamps) / 60.0 if self.message_timestamps else 0.0,
            "active_messages": len(self.messages),
            "response_time_percentiles": self._calculate_percentiles() if self.response_times else {}
        }
        
    def _calculate_percentiles(self) -> Dict[str, float]:
        """Calculate response time percentiles"""
        if not self.response_times:
            return {}
            
        sorted_times = sorted(self.response_times)
        length = len(sorted_times)
        
        return {
            "p50": sorted_times[int(length * 0.5)],
            "p90": sorted_times[int(length * 0.9)],
            "p95": sorted_times[int(length * 0.95)],
            "p99": sorted_times[int(length * 0.99)]
        }

class CommunicationErrorSimulator:
    """Simulates various communication errors for testing"""
    
    def __init__(self, error_rate: float = 0.0):
        self.logger = logging.getLogger("CommunicationErrorSimulator")
        self.error_rate = error_rate
        self.error_patterns = {}
        self.active_errors: Set[CommunicationError] = set()
        
    def set_error_rate(self, error_rate: float):
        """Set global error rate (0.0 to 1.0)"""
        self.error_rate = max(0.0, min(1.0, error_rate))
        
    def add_error_pattern(self, error_type: CommunicationError, 
                         probability: float, duration_seconds: float = 0.0):
        """Add specific error pattern"""
        self.error_patterns[error_type] = {
            "probability": probability,
            "duration": duration_seconds,
            "last_triggered": 0.0
        }
        
    def should_inject_error(self) -> Optional[CommunicationError]:
        """Determine if an error should be injected"""
        if random.random() > self.error_rate:
            return None
            
        # Check specific error patterns
        current_time = time.time()
        for error_type, pattern in self.error_patterns.items():
            if random.random() < pattern["probability"]:
                # Check if error is still active
                if error_type in self.active_errors:
                    time_since_trigger = current_time - pattern["last_triggered"]
                    if time_since_trigger < pattern["duration"]:
                        continue  # Error still active
                    else:
                        self.active_errors.discard(error_type)
                        
                # Trigger new error
                pattern["last_triggered"] = current_time
                if pattern["duration"] > 0:
                    self.active_errors.add(error_type)
                    
                return error_type
                
        # Random error selection
        return random.choice(list(CommunicationError))
        
    def simulate_error(self, error_type: CommunicationError, data: bytes) -> Tuple[bytes, bool]:
        """Simulate specific error on data"""
        if error_type == CommunicationError.CHECKSUM_ERROR:
            # Corrupt checksum
            if len(data) > 2:
                corrupted = bytearray(data)
                corrupted[-1] = (corrupted[-1] + 1) % 256
                return bytes(corrupted), False
                
        elif error_type == CommunicationError.FRAME_ERROR:
            # Corrupt frame structure
            if len(data) > 4:
                corrupted = bytearray(data)
                corrupted[1] = 0xFF  # Invalid frame marker
                return bytes(corrupted), False
                
        elif error_type == CommunicationError.BUFFER_OVERFLOW:
            # Simulate buffer overflow by truncating
            return data[:len(data)//2], False
            
        elif error_type == CommunicationError.TIMEOUT:
            # Indicate timeout (no response)
            return b'', False
            
        elif error_type == CommunicationError.CONNECTION_LOST:
            # Indicate connection lost
            return b'', False
            
        return data, True

class ProtocolLatencySimulator:
    """Simulates realistic protocol timing and latency"""
    
    def __init__(self):
        self.logger = logging.getLogger("ProtocolLatencySimulator")
        self.base_latency_ms = 10.0
        self.jitter_ms = 5.0
        self.network_congestion = 0.0  # 0.0 to 1.0
        
    def set_network_conditions(self, base_latency_ms: float, 
                              jitter_ms: float, congestion: float = 0.0):
        """Set network conditions for latency simulation"""
        self.base_latency_ms = base_latency_ms
        self.jitter_ms = jitter_ms
        self.network_congestion = max(0.0, min(1.0, congestion))
        
    async def simulate_latency(self) -> float:
        """Simulate network latency and return delay in seconds"""
        # Base latency with jitter
        latency = self.base_latency_ms + random.uniform(-self.jitter_ms, self.jitter_ms)

        # Add congestion effects
        if self.network_congestion > 0:
            # Use expovariate instead of exponential (lambda = 1/mean)
            congestion_delay = random.expovariate(1.0 / (self.network_congestion * 100))
            latency += congestion_delay

        # Ensure minimum latency
        latency = max(1.0, latency)

        # Convert to seconds and apply delay
        delay_seconds = latency / 1000.0
        await asyncio.sleep(delay_seconds)

        return latency

class LoadTestingFramework:
    """Load testing framework for protocol performance testing"""

    def __init__(self):
        self.logger = logging.getLogger("LoadTestingFramework")
        self.test_results = []
        self.active_tests = {}

    async def run_concurrent_session_test(self, protocol_type: str, host: str, port: int,
                                        concurrent_sessions: int, duration_seconds: int,
                                        requests_per_session: int = 100) -> Dict[str, Any]:
        """Run concurrent session load test"""
        test_id = f"{protocol_type}_load_{int(time.time())}"

        test_config = {
            "test_id": test_id,
            "protocol": protocol_type,
            "host": host,
            "port": port,
            "concurrent_sessions": concurrent_sessions,
            "duration_seconds": duration_seconds,
            "requests_per_session": requests_per_session,
            "start_time": datetime.now(timezone.utc)
        }

        self.active_tests[test_id] = test_config

        # Create session tasks
        session_tasks = []
        for session_id in range(concurrent_sessions):
            task = asyncio.create_task(
                self._run_session_load_test(test_id, session_id, protocol_type,
                                          host, port, duration_seconds, requests_per_session)
            )
            session_tasks.append(task)

        # Wait for all sessions to complete
        session_results = await asyncio.gather(*session_tasks, return_exceptions=True)

        # Compile results
        test_results = {
            "test_id": test_id,
            "config": test_config,
            "session_results": session_results,
            "end_time": datetime.now(timezone.utc),
            "summary": self._calculate_load_test_summary(session_results)
        }

        self.test_results.append(test_results)
        del self.active_tests[test_id]

        return test_results

    async def _run_session_load_test(self, test_id: str, session_id: int, protocol_type: str,
                                   host: str, port: int, duration_seconds: int,
                                   requests_per_session: int) -> Dict[str, Any]:
        """Run load test for single session"""
        session_results = {
            "session_id": session_id,
            "requests_sent": 0,
            "requests_successful": 0,
            "requests_failed": 0,
            "response_times": [],
            "errors": [],
            "start_time": datetime.now(timezone.utc)
        }

        end_time = time.time() + duration_seconds

        try:
            if protocol_type.upper() == "DNP3":
                await self._run_dnp3_load_test(session_results, host, port, end_time, requests_per_session)
            elif protocol_type.upper() == "MODBUS":
                await self._run_modbus_load_test(session_results, host, port, end_time, requests_per_session)
            elif protocol_type.upper() == "HTTP":
                await self._run_http_load_test(session_results, host, port, end_time, requests_per_session)

        except Exception as e:
            session_results["errors"].append(f"Session error: {str(e)}")

        session_results["end_time"] = datetime.now(timezone.utc)
        session_results["duration"] = (session_results["end_time"] - session_results["start_time"]).total_seconds()

        return session_results

    async def _run_dnp3_load_test(self, session_results: Dict, host: str, port: int,
                                end_time: float, max_requests: int):
        """Run DNP3 load test"""
        try:
            reader, writer = await asyncio.open_connection(host, port)

            while time.time() < end_time and session_results["requests_sent"] < max_requests:
                start_time = time.time()

                try:
                    # Send DNP3 read request with proper frame structure
                    # Start bytes (0x05, 0x64), Length (5), Control (0x44), Dest (1), Src (100)
                    header = struct.pack('<BBHH', 5, 0x44, 1, 100)
                    # Calculate CRC for header (using simple CRC for testing)
                    header_crc = self._calculate_simple_crc(header)

                    # Application layer: Control (0xC0), Function (READ=1), Object (30.1), Qualifier (0x06)
                    app_data = struct.pack('<BBBB', 0xC0, 0x01, 0x1E, 0x06)
                    app_crc = self._calculate_simple_crc(app_data)

                    dnp3_request = b'\x05\x64' + header + struct.pack('<H', header_crc) + app_data + struct.pack('<H', app_crc)
                    writer.write(dnp3_request)
                    await writer.drain()

                    # Wait for response
                    response = await asyncio.wait_for(reader.read(1024), timeout=5.0)

                    # Validate response (basic check for DNP3 frame)
                    if len(response) >= 10 and response[:2] == b'\x05\x64':
                        response_time = (time.time() - start_time) * 1000  # ms
                        session_results["response_times"].append(response_time)
                        session_results["requests_successful"] += 1
                    else:
                        session_results["errors"].append(f"Invalid DNP3 response: {len(response)} bytes")
                        session_results["requests_failed"] += 1

                except asyncio.TimeoutError:
                    session_results["errors"].append("DNP3 request timeout")
                    session_results["requests_failed"] += 1
                except Exception as e:
                    session_results["errors"].append(f"DNP3 request error: {str(e)}")
                    session_results["requests_failed"] += 1

                session_results["requests_sent"] += 1
                await asyncio.sleep(0.01)  # Small delay between requests

            writer.close()
            await writer.wait_closed()

        except Exception as e:
            session_results["errors"].append(f"DNP3 connection error: {str(e)}")

    async def _run_modbus_load_test(self, session_results: Dict, host: str, port: int,
                                  end_time: float, max_requests: int):
        """Run Modbus load test"""
        try:
            reader, writer = await asyncio.open_connection(host, port)

            transaction_id = 1

            while time.time() < end_time and session_results["requests_sent"] < max_requests:
                start_time = time.time()

                try:
                    # Send Modbus read holding registers request
                    # MBAP Header: Transaction ID (2), Protocol ID (2), Length (2), Unit ID (1)
                    # PDU: Function Code (1), Starting Address (2), Quantity (2)
                    modbus_request = struct.pack('>HHHBHH',
                                               transaction_id, 0, 6, 1, 3, 0, 10)  # Read 10 registers
                    writer.write(modbus_request)
                    await writer.drain()

                    # Wait for response
                    response = await asyncio.wait_for(reader.read(1024), timeout=5.0)

                    # Validate response (basic check for Modbus TCP response)
                    if len(response) >= 9:  # Minimum Modbus TCP response
                        # Parse MBAP header to verify transaction ID
                        resp_trans_id = struct.unpack('>H', response[:2])[0]
                        if resp_trans_id == transaction_id:
                            response_time = (time.time() - start_time) * 1000  # ms
                            session_results["response_times"].append(response_time)
                            session_results["requests_successful"] += 1
                        else:
                            session_results["errors"].append(f"Transaction ID mismatch: {resp_trans_id} != {transaction_id}")
                            session_results["requests_failed"] += 1
                    else:
                        session_results["errors"].append(f"Invalid Modbus response: {len(response)} bytes")
                        session_results["requests_failed"] += 1

                    transaction_id += 1

                except asyncio.TimeoutError:
                    session_results["errors"].append("Modbus request timeout")
                    session_results["requests_failed"] += 1
                except Exception as e:
                    session_results["errors"].append(f"Modbus request error: {str(e)}")
                    session_results["requests_failed"] += 1

                session_results["requests_sent"] += 1
                await asyncio.sleep(0.01)

            writer.close()
            await writer.wait_closed()

        except Exception as e:
            session_results["errors"].append(f"Modbus connection error: {str(e)}")

    async def _run_http_load_test(self, session_results: Dict, host: str, port: int,
                                end_time: float, max_requests: int):
        """Run HTTP load test"""
        try:
            async with aiohttp.ClientSession() as session:
                base_url = f"http://{host}:{port}"

                while time.time() < end_time and session_results["requests_sent"] < max_requests:
                    start_time = time.time()

                    try:
                        async with session.get(f"{base_url}/status") as response:
                            await response.read()

                            response_time = (time.time() - start_time) * 1000  # ms
                            session_results["response_times"].append(response_time)

                            if response.status < 400:
                                session_results["requests_successful"] += 1
                            else:
                                session_results["requests_failed"] += 1
                                session_results["errors"].append(f"HTTP {response.status}")

                    except asyncio.TimeoutError:
                        session_results["errors"].append("HTTP request timeout")
                        session_results["requests_failed"] += 1
                    except Exception as e:
                        session_results["errors"].append(f"HTTP request error: {str(e)}")
                        session_results["requests_failed"] += 1

                    session_results["requests_sent"] += 1
                    await asyncio.sleep(0.01)

        except Exception as e:
            session_results["errors"].append(f"HTTP session error: {str(e)}")

    def _calculate_simple_crc(self, data: bytes) -> int:
        """Calculate simple CRC for testing purposes (not production DNP3 CRC)"""
        crc = 0
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc & 0xFFFF

    def _calculate_load_test_summary(self, session_results: List[Dict]) -> Dict[str, Any]:
        """Calculate load test summary statistics"""
        total_requests = sum(r.get("requests_sent", 0) for r in session_results if isinstance(r, dict))
        total_successful = sum(r.get("requests_successful", 0) for r in session_results if isinstance(r, dict))
        total_failed = sum(r.get("requests_failed", 0) for r in session_results if isinstance(r, dict))

        all_response_times = []
        for r in session_results:
            if isinstance(r, dict) and "response_times" in r:
                all_response_times.extend(r["response_times"])

        summary = {
            "total_requests": total_requests,
            "successful_requests": total_successful,
            "failed_requests": total_failed,
            "success_rate": (total_successful / total_requests * 100) if total_requests > 0 else 0,
            "average_response_time": sum(all_response_times) / len(all_response_times) if all_response_times else 0,
            "min_response_time": min(all_response_times) if all_response_times else 0,
            "max_response_time": max(all_response_times) if all_response_times else 0,
            "requests_per_second": total_requests / max(1, sum(r.get("duration", 1) for r in session_results if isinstance(r, dict))) if session_results else 0
        }

        # Calculate percentiles
        if all_response_times:
            sorted_times = sorted(all_response_times)
            length = len(sorted_times)
            summary["response_time_percentiles"] = {
                "p50": sorted_times[int(length * 0.5)],
                "p90": sorted_times[int(length * 0.9)],
                "p95": sorted_times[int(length * 0.95)],
                "p99": sorted_times[int(length * 0.99)]
            }

        return summary

    def get_load_test_results(self) -> List[Dict[str, Any]]:
        """Get all load test results"""
        return self.test_results.copy()

class SecurityTestingFramework:
    """Security testing framework for protocol authentication"""

    def __init__(self):
        self.logger = logging.getLogger("SecurityTestingFramework")
        self.valid_credentials = {
            "admin": "password123",
            "operator": "operator456",
            "readonly": "readonly789"
        }
        self.failed_attempts = {}
        self.lockout_threshold = 3
        self.lockout_duration = 300  # 5 minutes
        
    def authenticate(self, username: str, password: str, client_ip: str) -> Tuple[bool, str]:
        """Authenticate user credentials"""
        current_time = time.time()
        
        # Check if client is locked out
        if client_ip in self.failed_attempts:
            attempts, last_attempt = self.failed_attempts[client_ip]
            if attempts >= self.lockout_threshold:
                if current_time - last_attempt < self.lockout_duration:
                    return False, "Account locked due to too many failed attempts"
                else:
                    # Reset after lockout period
                    del self.failed_attempts[client_ip]
                    
        # Validate credentials
        if username in self.valid_credentials:
            if self.valid_credentials[username] == password:
                # Reset failed attempts on successful login
                if client_ip in self.failed_attempts:
                    del self.failed_attempts[client_ip]
                return True, "Authentication successful"
                
        # Record failed attempt
        if client_ip not in self.failed_attempts:
            self.failed_attempts[client_ip] = [0, current_time]
        self.failed_attempts[client_ip][0] += 1
        self.failed_attempts[client_ip][1] = current_time
        
        return False, "Invalid credentials"
        
    def generate_test_credentials(self) -> List[Tuple[str, str]]:
        """Generate test credentials for security testing"""
        test_creds = [
            ("admin", "password123"),  # Valid
            ("admin", "wrongpass"),    # Invalid password
            ("baduser", "password"),   # Invalid user
            ("", ""),                  # Empty credentials
            ("admin", ""),             # Empty password
            ("operator", "operator456"), # Valid
            ("readonly", "readonly789"), # Valid
            ("admin", "admin"),        # Common weak password
            ("root", "root"),          # Common default
            ("test", "test")           # Test credentials
        ]
        return test_creds
        
    def simulate_authentication_attack(self, target_protocol: str) -> Dict[str, Any]:
        """Simulate authentication attack for testing"""
        attack_results = {
            "protocol": target_protocol,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "attempts": [],
            "successful_logins": 0,
            "failed_logins": 0,
            "lockouts_triggered": 0
        }
        
        test_creds = self.generate_test_credentials()
        client_ip = "*************"  # Simulated attacker IP
        
        for username, password in test_creds:
            success, message = self.authenticate(username, password, client_ip)
            
            attempt = {
                "username": username,
                "password": password,
                "success": success,
                "message": message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            attack_results["attempts"].append(attempt)
            
            if success:
                attack_results["successful_logins"] += 1
            else:
                attack_results["failed_logins"] += 1
                if "locked" in message.lower():
                    attack_results["lockouts_triggered"] += 1
                    
        attack_results["end_time"] = datetime.now(timezone.utc).isoformat()
        
        self.logger.info(f"Authentication attack simulation completed: "
                        f"{attack_results['successful_logins']} successful, "
                        f"{attack_results['failed_logins']} failed, "
                        f"{attack_results['lockouts_triggered']} lockouts")
        
        return attack_results
